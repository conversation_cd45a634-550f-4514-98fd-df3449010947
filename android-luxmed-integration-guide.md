# 🚀 Power Prompt: Android Integration with MyHealth LuxMed Sync API

## 📋 Overview
You are integrating an Android app with a Spring Boot backend service that synchronizes medical data from LuxMed (Polish healthcare provider). The backend exposes a REST API endpoint that requires specific authentication components extracted from LuxMed's web portal.

## 🔗 API Endpoint Details

### Base Information
- **Server**: Likely running on `http://localhost:8080` (development) or your deployed server URL
- **Endpoint**: `POST /api/v1/sync/luxmed`
- **Content-Type**: `application/json`
- **Authentication**: Bearer token (Firebase ID token) in Authorization header

### 📝 Request Structure

**Complete Request JSON Structure:**
```json
{
  "jwtToken": "jwt_token_from_authorization_header",
  "aspNetSessionId": "session_id_from_cookie", 
  "lxToken": "lx_token_from_cookie",
  "refreshToken": "refresh_token_from_cookie",
  "userAdditionalInfo": "user_info_jwt_from_cookie",
  "xsrfToken": "xsrf_token_from_cookie",
  "incapsulaSessionId": "optional_incapsula_session",
  "deviceId": "optional_device_id"
}
```

### 📤 Response Structure

**Success Response (200 OK):**
```json
{
  "success": true,
  "message": "LuxMed data synchronized successfully"
}
```

**Error Responses:**
- **401 Unauthorized**: Missing/invalid Firebase authentication
- **400 Bad Request**: Invalid LuxMed authentication components
- **500 Internal Server Error**: Synchronization failed

## 🔐 Authentication Requirements

### 1. Firebase Authentication (Required)
- User must be authenticated with Firebase
- Include Firebase ID token in Authorization header: `Bearer <firebase_id_token>`

### 2. LuxMed Authentication Components (Required)
The user must provide these 6 essential components from LuxMed portal:

| Field | Source | Required | Description |
|-------|--------|----------|-------------|
| `jwtToken` | Authorization header | ✅ | JWT token from LuxMed API calls |
| `aspNetSessionId` | Cookie | ✅ | ASP.NET session identifier |
| `lxToken` | Cookie | ✅ | LuxMed authentication token |
| `refreshToken` | Cookie | ✅ | Session refresh token |
| `userAdditionalInfo` | Cookie | ✅ | User permissions JWT |
| `xsrfToken` | Cookie/Header | ✅ | CSRF protection token |
| `incapsulaSessionId` | Cookie | ❌ | Optional bot protection |
| `deviceId` | Custom | ❌ | Optional device identification |

## 🛠️ Android Implementation Guide

### 1. Data Classes
```kotlin
data class LuxmedSyncRequest(
    val jwtToken: String,
    val aspNetSessionId: String,
    val lxToken: String,
    val refreshToken: String,
    val userAdditionalInfo: String,
    val xsrfToken: String,
    val incapsulaSessionId: String? = null,
    val deviceId: String? = null
)

data class SynchronizationResponse(
    val success: Boolean,
    val message: String
)
```

### 2. API Service Interface
```kotlin
interface MyHealthApiService {
    @POST("api/v1/sync/luxmed")
    suspend fun syncLuxmedData(
        @Header("Authorization") authorization: String,
        @Body request: LuxmedSyncRequest
    ): Response<SynchronizationResponse>
}
```

### 3. Repository Implementation
```kotlin
class HealthDataRepository(
    private val apiService: MyHealthApiService,
    private val firebaseAuth: FirebaseAuth
) {
    suspend fun syncLuxmedData(luxmedAuth: LuxmedAuthData): Result<String> {
        return try {
            // Get Firebase ID token
            val firebaseUser = firebaseAuth.currentUser 
                ?: return Result.failure(Exception("User not authenticated"))
            
            val idToken = firebaseUser.getIdToken(false).await().token
                ?: return Result.failure(Exception("Failed to get ID token"))
            
            // Prepare request
            val request = LuxmedSyncRequest(
                jwtToken = luxmedAuth.jwtToken,
                aspNetSessionId = luxmedAuth.aspNetSessionId,
                lxToken = luxmedAuth.lxToken,
                refreshToken = luxmedAuth.refreshToken,
                userAdditionalInfo = luxmedAuth.userAdditionalInfo,
                xsrfToken = luxmedAuth.xsrfToken,
                incapsulaSessionId = luxmedAuth.incapsulaSessionId,
                deviceId = luxmedAuth.deviceId
            )
            
            // Make API call
            val response = apiService.syncLuxmedData("Bearer $idToken", request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.success == true) {
                    Result.success(body.message)
                } else {
                    Result.failure(Exception(body?.message ?: "Sync failed"))
                }
            } else {
                Result.failure(Exception("HTTP ${response.code()}: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

### 4. UI Integration Example
```kotlin
class SyncViewModel(
    private val repository: HealthDataRepository
) : ViewModel() {
    
    private val _syncState = MutableLiveData<SyncState>()
    val syncState: LiveData<SyncState> = _syncState
    
    fun syncLuxmedData(luxmedAuth: LuxmedAuthData) {
        viewModelScope.launch {
            _syncState.value = SyncState.Loading
            
            repository.syncLuxmedData(luxmedAuth)
                .onSuccess { message ->
                    _syncState.value = SyncState.Success(message)
                }
                .onFailure { error ->
                    _syncState.value = SyncState.Error(error.message ?: "Unknown error")
                }
        }
    }
}

sealed class SyncState {
    object Loading : SyncState()
    data class Success(val message: String) : SyncState()
    data class Error(val message: String) : SyncState()
}
```

## 🔍 How Users Get LuxMed Authentication Data

Users need to:
1. **Log into LuxMed Portal**: Visit `https://portalpacjenta.luxmed.pl`
2. **Extract Authentication Data**: Use browser developer tools to capture:
   - **Network tab**: Find API calls to get `jwtToken` from Authorization header
   - **Application/Storage tab**: Extract cookies (`aspNetSessionId`, `lxToken`, `refreshToken`, `userAdditionalInfo`)
   - **Network headers**: Find `X-XSRF-TOKEN` or similar CSRF token

## ⚠️ Important Considerations

### Security
- **Never log sensitive tokens** in production
- Store LuxMed tokens securely (encrypted SharedPreferences/Keystore)
- Implement token refresh logic if needed
- Validate all required fields before API calls

### Error Handling
- Handle network timeouts (30s timeout configured on backend)
- Implement retry logic for transient failures
- Provide clear error messages to users
- Handle authentication expiration gracefully

### User Experience
- Show loading states during sync
- Provide progress feedback
- Allow users to cancel long-running operations
- Cache sync status to avoid unnecessary calls

## 🧪 Testing

### Test Data Available
The backend includes test data in `test_sync_request.json` - you can use similar structure for your tests.

### Test Scenarios
1. **Valid sync request** → 200 OK with success message
2. **Missing Firebase auth** → 401 Unauthorized
3. **Invalid LuxMed tokens** → 400 Bad Request
4. **Network failure** → Handle gracefully
5. **Backend unavailable** → Retry logic

## 📚 Additional Resources

- **Backend Architecture**: Clean Architecture with Domain/Data/API layers
- **Technology Stack**: Spring Boot 3.x, WebFlux (reactive), MongoDB
- **Authentication**: Firebase Admin SDK for token validation
- **LuxMed Integration**: Fetches lab results and visit data from LuxMed Patient Portal

## 🎯 Success Criteria

Your integration should:
- ✅ Authenticate users with Firebase
- ✅ Collect all required LuxMed authentication components
- ✅ Make successful API calls to sync endpoint
- ✅ Handle all error scenarios gracefully
- ✅ Provide clear feedback to users
- ✅ Store authentication data securely

---

This guide provides everything needed to successfully integrate with the LuxMed sync endpoint, including complete code examples, error handling, and security considerations.
